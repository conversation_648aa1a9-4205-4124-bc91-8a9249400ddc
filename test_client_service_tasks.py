#!/usr/bin/env python3
"""
Simple test to verify client service task generation functionality.
"""
import datetime
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from records.api.client_services import _calculate_due_date, _generate_task_name, _generate_task_description
from records.db.models import ServicePriceType


class MockService:
    """Mock service object for testing."""
    def __init__(self, title, price_type, price=100.0, description=None):
        self.title = title
        self.price_type = price_type
        self.price = price
        self.description = description


def test_calculate_due_date():
    """Test due date calculation for different price types."""
    start_date = datetime.datetime(2025, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
    
    # Test monthly
    monthly_due = _calculate_due_date(start_date, ServicePriceType.MONTHLY)
    expected_monthly = start_date + datetime.timedelta(days=30)
    assert monthly_due == expected_monthly, f"Monthly due date mismatch: {monthly_due} != {expected_monthly}"
    
    # Test quarterly
    quarterly_due = _calculate_due_date(start_date, ServicePriceType.QUARTERLY)
    expected_quarterly = start_date + datetime.timedelta(days=90)
    assert quarterly_due == expected_quarterly, f"Quarterly due date mismatch: {quarterly_due} != {expected_quarterly}"
    
    # Test annual
    annual_due = _calculate_due_date(start_date, ServicePriceType.ANNUAL)
    expected_annual = start_date + datetime.timedelta(days=365)
    assert annual_due == expected_annual, f"Annual due date mismatch: {annual_due} != {expected_annual}"
    
    # Test one-time
    onetime_due = _calculate_due_date(start_date, ServicePriceType.ONE_TIME)
    expected_onetime = start_date + datetime.timedelta(days=7)
    assert onetime_due == expected_onetime, f"One-time due date mismatch: {onetime_due} != {expected_onetime}"
    
    # Test unknown price type (should default to 30 days)
    unknown_due = _calculate_due_date(start_date, "UNKNOWN")
    expected_unknown = start_date + datetime.timedelta(days=30)
    assert unknown_due == expected_unknown, f"Unknown price type due date mismatch: {unknown_due} != {expected_unknown}"
    
    print("✓ All due date calculations passed!")


def test_generate_task_name():
    """Test task name generation for different price types."""
    
    # Test monthly service
    monthly_service = MockService("Tax Preparation", ServicePriceType.MONTHLY)
    monthly_name = _generate_task_name(monthly_service)
    expected_monthly = "Tax Preparation - Monthly Service"
    assert monthly_name == expected_monthly, f"Monthly task name mismatch: {monthly_name} != {expected_monthly}"
    
    # Test quarterly service
    quarterly_service = MockService("Bookkeeping", ServicePriceType.QUARTERLY)
    quarterly_name = _generate_task_name(quarterly_service)
    expected_quarterly = "Bookkeeping - Quarterly Service"
    assert quarterly_name == expected_quarterly, f"Quarterly task name mismatch: {quarterly_name} != {expected_quarterly}"
    
    # Test annual service
    annual_service = MockService("Annual Report", ServicePriceType.ANNUAL)
    annual_name = _generate_task_name(annual_service)
    expected_annual = "Annual Report - Annual Service"
    assert annual_name == expected_annual, f"Annual task name mismatch: {annual_name} != {expected_annual}"
    
    # Test one-time service
    onetime_service = MockService("Business Setup", ServicePriceType.ONE_TIME)
    onetime_name = _generate_task_name(onetime_service)
    expected_onetime = "Business Setup - One-time Service"
    assert onetime_name == expected_onetime, f"One-time task name mismatch: {onetime_name} != {expected_onetime}"
    
    # Test service with no price type
    no_type_service = MockService("Consultation", None)
    no_type_name = _generate_task_name(no_type_service)
    expected_no_type = "Consultation"
    assert no_type_name == expected_no_type, f"No price type task name mismatch: {no_type_name} != {expected_no_type}"
    
    print("✓ All task name generation tests passed!")


def test_generate_task_description():
    """Test task description generation."""
    
    # Test service with description
    service_with_desc = MockService(
        "Tax Preparation", 
        ServicePriceType.MONTHLY, 
        150.0, 
        "Complete monthly tax preparation and filing"
    )
    desc_with_desc = _generate_task_description(service_with_desc)
    expected_lines = [
        "Complete service: Tax Preparation",
        "",
        "Service Description: Complete monthly tax preparation and filing",
        "Service Price: $150.00 (Monthly)"
    ]
    expected_with_desc = "\n".join(expected_lines)
    assert desc_with_desc == expected_with_desc, f"Description with desc mismatch:\n{desc_with_desc}\n!=\n{expected_with_desc}"
    
    # Test service without description
    service_no_desc = MockService("Bookkeeping", ServicePriceType.QUARTERLY, 300.0)
    desc_no_desc = _generate_task_description(service_no_desc)
    expected_no_desc = "Complete service: Bookkeeping\nService Price: $300.00 (Quarterly)"
    assert desc_no_desc == expected_no_desc, f"Description without desc mismatch:\n{desc_no_desc}\n!=\n{expected_no_desc}"
    
    print("✓ All task description generation tests passed!")


def main():
    """Run all tests."""
    print("Running client service task generation tests...")
    print()
    
    try:
        test_calculate_due_date()
        test_generate_task_name()
        test_generate_task_description()
        
        print()
        print("🎉 All tests passed successfully!")
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
