import datetime
import os
from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel

from records.api import auth_utils, common_utils, addresses
from records.db import api as db_api


class RegAgentBase(BaseModel):
    nickname: Optional[str] = None
    address_id: Optional[str] = None
    address: Optional[addresses.AddressUpdate] = None


class RegAgentCreate(RegAgentBase):
    id: Optional[str] = None
    title: str


class RegAgentUpdate(RegAgentBase):
    id: Optional[str] = None
    title: Optional[str] = None


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'reg_agents'),
        dependencies=[Depends(auth_utils.basic_access)]
    )

    router.add_api_route("", list_reg_agents, methods=['GET'], name='List reg_agents')
    router.add_api_route("", create_reg_agent, methods=['POST'], name='Create reg_agent')
    router.add_api_route("/{reg_agent_id}", get_reg_agent, methods=['GET'], name='Get reg_agent')
    router.add_api_route("/{reg_agent_id}", update_reg_agent, methods=['PUT'], name='Update reg_agent')
    router.add_api_route("/{reg_agent_id}", delete_reg_agent, methods=['DELETE'], name='Delete reg_agent')

    return router


async def list_reg_agents(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    reg_agents, count = await db_api.list_reg_agents(limit=limit, page=page, order=order, desc=desc, q=q)
    return {'items': reg_agents, 'count': count, 'limit': limit, 'page': page}


async def get_reg_agent(reg_agent_id: str):
    db_reg_agent = await db_api.get_reg_agent_by_id(reg_agent_id)
    return db_reg_agent


async def create_reg_agent(reg_agent: RegAgentCreate):
    reg_agent_dict = reg_agent.model_dump(exclude_unset=True)
    reg_agent_dict.pop('id', None)
    address_id = reg_agent_dict.pop('address_id', None) or reg_agent_dict.pop('address', {}).get('id')
    if address_id:
        reg_agent_dict['address_id'] = address_id

    db_reg_agent = await db_api.create_reg_agent(reg_agent_dict)
    return db_reg_agent


async def update_reg_agent(reg_agent_id: str, reg_agent: RegAgentUpdate):
    db_reg_agent = await db_api.get_reg_agent_by_id(reg_agent_id)

    reg_agent_dict = reg_agent.model_dump(exclude_unset=True)
    address_id = reg_agent_dict.pop('address_id', None) or reg_agent_dict.pop('address', {}).get('id')
    if address_id:
        reg_agent_dict['address_id'] = address_id

    updated_reg_agent = await db_api.update_reg_agent(db_reg_agent, reg_agent_dict)
    return updated_reg_agent


async def delete_reg_agent(reg_agent_id: str):
    db_reg_agent = await db_api.get_reg_agent_by_id(reg_agent_id)
    await db_api.delete_reg_agent(reg_agent_id)
    return None
