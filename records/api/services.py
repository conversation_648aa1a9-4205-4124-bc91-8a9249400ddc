import os
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, Response
from pydantic import BaseModel

from records.api import auth_utils
from records.db import api as db_api, base


class ServiceComponentCreate(BaseModel):
    title: str
    description: Optional[str] = None
    price: Optional[float] = None
    price_type: Optional[str] = None


class ServiceCreate(BaseModel):
    title: str
    description: Optional[str] = None
    price: float
    price_type: str
    components: Optional[List[ServiceComponentCreate]] = None
    

class ServiceUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    price_type: Optional[str] = None
    components: Optional[List[ServiceComponentCreate]] = None


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'services'),
        dependencies=[Depends(auth_utils.owner_access)]
    )

    router.add_api_route("", list_services, methods=['GET'], name='List services')
    router.add_api_route("", create_service, methods=['POST'], name='Create service')
    router.add_api_route("/by-id/{service_id}", get_service_by_id, methods=['GET'], name='Get service by ID')
    router.add_api_route("/by-id/{service_id}", update_service_by_id, methods=['PUT'], name='Update service by ID')
    router.add_api_route("/{service_title}", get_service, methods=['GET'], name='Get service')
    router.add_api_route("/{service_title}", update_service, methods=['PUT'], name='Update service')
    router.add_api_route("/{service_title}", delete_service, methods=['DELETE'], name='Delete service')

    return router


async def _get_service(service_title: str = None, service_id: str = None):
    """Common logic for retrieving a service by title or ID with components."""
    if not service_title and not service_id:
        raise ValueError("Either service_title or service_id must be provided")

    async with base.session_context():
        if service_title:
            db_service = await db_api.get_service_by_title(service_title)
            if not db_service:
                raise HTTPException(404, f'Service {service_title} not found')
        else:
            db_service = await db_api.get_service_by_id(service_id)
            if not db_service:
                raise HTTPException(404, f'Service id={service_id} not found')

        components = await db_api.list_service_components(service_id=db_service.id)
        db_service.components = components

    return db_service.to_dict()


async def _update_service(db_service, service: ServiceUpdate):
    """Common logic for updating a service with components."""
    service_dict = service.model_dump(exclude_unset=True)
    if not service_dict:
        raise HTTPException(400, 'No fields to update')

    async with base.session_context():
        service_dict['composite_flag'] = bool(service.components)
        components = service_dict.pop('components', None)
        updated_service = await db_api.update_service(db_service, service_dict)

        # Delete all components
        await db_api.delete_service_components(service_id=db_service.id)
        components_db = []
        # Create components
        if components:
            for idx, component_dict in enumerate(components):
                component_dict['service_id'] = db_service.id
                component_dict['sequence'] = idx
                component_dict['price_type'] = component_dict.get('price_type') or service_dict.get('price_type')
                component_db = await db_api.create_service_component(component_dict)
                components_db.append(component_db)

    service_dict = updated_service.to_dict()
    service_dict['components'] = [c.to_dict() for c in components_db]
    return service_dict


async def list_services(
    q: str = None,
    # session: AsyncSession = Depends(get_session)
):
    services = await db_api.list_services(q=q)
    service_dicts = [m.to_dict() for m in services]

    return {'items': service_dicts, 'count': len(service_dicts)}


async def get_service(service_title: str):
    return await _get_service(service_title=service_title)


async def get_service_by_id(service_id: str):
    return await _get_service(service_id=service_id)


async def create_service(service: ServiceCreate):
    service_dict = service.model_dump()
    async with base.session_context():
        db_service = await db_api.get_service_by_title(title=service_dict['title'])
        if db_service:
            raise HTTPException(400, f'Service {service_dict["title"]} already exists')

        components = service_dict.pop('components', [])
        service_dict['composite_flag'] = bool(components)
        db_service = await db_api.create_service(service_dict)
        components_db = []
        for idx, component_dict in enumerate(components):
            component_dict['service_id'] = db_service.id
            component_dict['sequence'] = idx
            component_dict['price_type'] = component_dict.get('price_type') or service_dict['price_type']
            component_db = await db_api.create_service_component(component_dict)
            components_db.append(component_db)

    service_dict = db_service.to_dict()
    service_dict['components'] = [c.to_dict() for c in components_db]
    return service_dict


async def update_service(service_title: str, service: ServiceUpdate):
    db_service = await db_api.get_service_by_title(service_title)
    if not db_service:
        raise HTTPException(404, f'Service {service_title} not found')

    return await _update_service(db_service, service)


async def update_service_by_id(service_id: str, service: ServiceUpdate):
    db_service = await db_api.get_service_by_id(service_id)
    if not db_service:
        raise HTTPException(404, f'Service id={service_id} not found')

    return await _update_service(db_service, service)


async def delete_service(service_title: str):
    db_service = await db_api.get_service_by_title(service_title)

    async with base.session_context():
        await db_api.delete_service(service_title)

    return Response(status_code=204)
