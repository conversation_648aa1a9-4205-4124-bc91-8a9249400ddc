import datetime

import sqlalchemy as sa
from sqlalchemy.orm import relationship
from sqlalchemy import types as st
from sqlalchemy.dialects.postgresql import JSO<PERSON> as PostgresJSON
from sqlalchemy.dialects.postgresql import JSON<PERSON> as PostgresJSONB

from records.db import base
from records.utils import utils, blowfish_utils


class TimelineType:
    MAIN = 'main'
    CLIENT_DATA = 'client_data'
    APPROVED_DATA = 'approved_data'


class ExtractorType:
    MAIN = 'main'
    GLOBAL = 'global'


class ChangeRecordType:
    MANUAL = 'manual'
    PENDING_CHANGE = 'pending_change'


class ServicePriceType:
    ONE_TIME = 'One-time'
    MONTHLY = 'Monthly'
    QUARTERLY = 'Quarterly'
    ANNUAL = 'Annual'


class TaskStatus:
    OPEN = 'OPEN'
    IN_PROGRESS = 'IN_PROGRESS'
    COMPLETED = 'COMPLETED'


def now():
    return datetime.datetime.now(datetime.timezone.utc)


class Session(base.ModelBase):
    __tablename__ = 'sessions'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_uuid
    )
    ttl = sa.Column(sa.DateTime(timezone=True), nullable=True)
    admin = sa.Column(st.Boolean())
    user_id = sa.Column(st.BigInteger, nullable=False, index=True)
    extra = sa.Column(st.Text())

    def to_dict(self):
        d = super(Session, self).to_dict()
        if not self.admin:
            del d['admin']
        return d


class User(base.ModelBase):
    __tablename__ = 'users'

    id = sa.Column(
        sa.BigInteger(),
        primary_key=True,
    )
    name = sa.Column(sa.String(200))
    info = sa.Column(PostgresJSON())
    login = sa.Column(sa.String(200))
    password = sa.Column(sa.String(200))
    confirmed = sa.Column(sa.Boolean())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    last_seen_at = sa.Column(sa.DateTime(timezone=True), nullable=True)
    last_login_at = sa.Column(sa.DateTime(timezone=True), nullable=True)
    last_activity_at = sa.Column(sa.DateTime(timezone=True), nullable=True)

    def to_dict(self):
        d = super(User, self).to_dict()
        del d['password']

        d['info'] = utils.maybe_dict(d['info'])
        return d


class UserConfirm(base.ModelBase):
    __tablename__ = 'user_confirms'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
    )
    user_id = sa.Column(sa.BigInteger())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    expiry_time = sa.Column(sa.DateTime(timezone=True), nullable=False)


class UserInvite(base.ModelBase):
    __tablename__ = 'user_invites'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
    )
    from_user_id = sa.Column(sa.BigInteger())
    to_user_id = sa.Column(sa.BigInteger())
    to_user_login = sa.Column(sa.String(200))
    # to_workspace_id = sa.Column(sa.Integer())
    # to_group_id = sa.Column(sa.Integer())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    expiry_time = sa.Column(sa.DateTime(timezone=True), nullable=False)


class UserServiceAccount(base.ModelBase):
    __tablename__ = 'user_service_accounts'

    id = sa.Column(
        sa.Integer(),
        primary_key=True,
    )
    user_id = sa.Column(sa.BigInteger(), index=True)
    service = sa.Column(sa.String(200), index=True)
    service_id = sa.Column(sa.String(200))
    service_name = sa.Column(sa.String(200))
    service_picture = sa.Column(sa.String(255))
    token = sa.Column(sa.Text())
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    def get_token(self):
        if self.token:
            if isinstance(self.token, dict):
                return self.token
            return blowfish_utils.decrypt_dict(self.token)
        else:
            return {}

    def set_token(self, token: dict):
        self.token = blowfish_utils.encrypt_dict(token)


class Token(base.ModelBase):
    __tablename__ = 'tokens'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_uuid
    )
    owner_id = sa.Column(sa.BigInteger())
    object_id = sa.Column(sa.BigInteger(), index=True)
    object_type = sa.Column(sa.String(30), index=True)
    # If type == user then permissions should be inherited from the user and must be NULL here
    permissions = sa.Column(sa.BigInteger(), nullable=True)
    # org_permissions = sa.Column(sa.BigInteger(), nullable=True)
    description = sa.Column(sa.String(255))
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    expiry_time = sa.Column(sa.DateTime(timezone=True), nullable=False)


class Address(base.ModelBase):
    __tablename__ = 'addresses'

    id = sa.Column(
        sa.String(36),
        primary_key=True,
        default=utils.generate_unicode_uuid,
    )
    full_address = sa.Column(sa.String)
    street = sa.Column(sa.String)
    pobox = sa.Column(sa.String)
    city = sa.Column(sa.String)
    state = sa.Column(sa.String)
    zip = sa.Column(sa.String)
    country = sa.Column(sa.String)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    __table_args__ = (
        sa.PrimaryKeyConstraint('id', name='addresses_pkey'),
    )


class Catalog(base.ModelBase):
    __tablename__ = 'catalogs'

    id = sa.Column(sa.Integer, primary_key=True)
    name = sa.Column(sa.String)
    options = sa.Column(PostgresJSON)  # Storing options as a PostgresJSONB field


class Person(base.ModelBase):
    __tablename__ = 'client_persons'

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    full_title = sa.Column(sa.String)
    firstname = sa.Column(sa.String)
    lastname = sa.Column(sa.String)
    email = sa.Column(sa.String)
    phone = sa.Column(sa.String(200))
    pcm = sa.Column(sa.String)
    citizenship = sa.Column(sa.String)
    address = sa.Column(sa.Text)  # This could be a sa.ForeignKey to Address.id if needed
    companies = sa.Column(sa.String)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class Manager(base.ModelBase):
    __tablename__ = 'managers'

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    user_id = sa.Column(sa.BigInteger)
    title = sa.Column(sa.String)
    email = sa.Column(sa.String)
    phone = sa.Column(sa.String)
    permissions = sa.Column(sa.BigInteger)
    role_name = sa.Column(sa.String(100))

    person_id = sa.Column(sa.String(36), sa.ForeignKey('client_persons.id'), nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # assigned_tasks = relationship('Task', foreign_keys='Task.assignee_manager_id', cascade="all, delete-orphan")
    # assigned_tax_reports = relationship('TaxReport', foreign_keys='TaxReport.assignee_manager_id', cascade="all, delete-orphan")
    # assigned_client_tasks = relationship('ClientTask', foreign_keys='ClientTask.manager_id', cascade="all, delete-orphan")


class RegAgent(base.ModelBase):
    __tablename__ = 'reg_agents'
    __to_dict__ = ['address']

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    title = sa.Column(sa.String)
    nickname = sa.Column(sa.String)
    address_id = sa.Column(sa.String(36), sa.ForeignKey('addresses.id'), nullable=True)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class Service(base.ModelBase):
    __tablename__ = 'services'
    __to_dict__ = ['components']

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    title = sa.Column(sa.String, nullable=False)
    price = sa.Column(sa.Float, nullable=True)
    price_type = sa.Column(sa.String)  # One-time, Monthly, Quarterly, Annual
    description = sa.Column(sa.Text)

    composite_flag = sa.Column(sa.Boolean, default=False)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # child_components = relationship('ServiceComponent', foreign_keys='ServiceComponent.service_id')


class ServiceComponent(base.ModelBase):
    __tablename__ = 'service_components'

    id = sa.Column(sa.Integer, primary_key=True)
    title = sa.Column(sa.String, nullable=False)
    price = sa.Column(sa.Float, nullable=True)
    price_type = sa.Column(sa.String)  # One-time, Monthly, Quarterly, Annual
    description = sa.Column(sa.Text)
    service_id = sa.Column(sa.String(36), sa.ForeignKey('services.id'), nullable=False)
    sequence = sa.Column(sa.Integer, nullable=False)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    parent_service = relationship('Service', foreign_keys=[service_id], primaryjoin="ServiceComponent.service_id == Service.id")


class ClientService(base.ModelBase):
    __tablename__ = 'client_services'
    __to_dict__ = ['service', 'components']

    id = sa.Column(sa.Integer, primary_key=True, server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1))
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'), index=True)
    service_id = sa.Column(sa.String(36), sa.ForeignKey('services.id'))
    start_date = sa.Column(sa.DateTime(timezone=True))
    end_date = sa.Column(sa.DateTime(timezone=True))
    discount_percent = sa.Column(sa.Integer, nullable=True)
    discount_amount = sa.Column(sa.String(12), nullable=True)
    note = sa.Column(sa.Text, nullable=True)
    total = sa.Column(sa.String(12), nullable=True)  # total price for the service with discounts applied

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # service = relationship('Service', foreign_keys=[service_id], primaryjoin="ClientService.service_id == Service.id")
    # tasks = relationship('Task', cascade="all, delete-orphan")
    # components = relationship('ClientServiceComponent', cascade="all, delete-orphan")


class ClientServiceComponent(base.ModelBase):
    __tablename__ = 'client_service_components'

    id = sa.Column(sa.Integer, primary_key=True)
    client_service_id = sa.Column(sa.Integer, sa.ForeignKey('client_services.id'), nullable=False)
    # components fields
    service_component_id = sa.Column(sa.Integer, nullable=False)

    title = sa.Column(sa.String, nullable=False)
    price = sa.Column(sa.Float, nullable=True)
    price_type = sa.Column(sa.String)  # One-time, Monthly, Quarterly, Annual
    description = sa.Column(sa.Text)
    sequence = sa.Column(sa.Integer, nullable=False)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # client_service = relationship('ClientService', foreign_keys=[client_service_id], primaryjoin="ClientServiceComponent.client_service_id == ClientService.id")
    # service = relationship('Service', foreign_keys=[service_id], primaryjoin="ClientServiceComponent.service_id == Service.id")


class TaxReport(base.ModelBase):
    __tablename__ = 'tax_reports'

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'), nullable=False)
    name = sa.Column(sa.String, nullable=False)
    fiscal_year = sa.Column(sa.String, nullable=False)
    assignee_manager_id = sa.Column(sa.String(36), sa.ForeignKey('managers.id'))
    status = sa.Column(sa.String, default='DOCUMENT_COLLECTION')  # DOCUMENT_COLLECTION, PREPARATION, REVIEW, AWAITING_SIGNATURE, FILED, DONE
    # progress_steps = sa.Column(PostgresJSON)
    note = sa.Column(sa.Text)
    file_id = sa.Column(sa.String(36), sa.ForeignKey('files.id'), nullable=True)
    file_name = sa.Column(sa.String, nullable=True)

    # notification/burning status tracking
    is_burning = sa.Column(sa.Boolean, default=False)  # For showing "подгорающие" tax reports
    notification_triggered_at = sa.Column(sa.DateTime(timezone=True))  # When notification was first triggered
    notification_dismissed_at = sa.Column(sa.DateTime(timezone=True))  # When notification was manually dismissed
    due_date = sa.Column(sa.DateTime(timezone=True))  # Due date for tax report

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # client = relationship('Client', foreign_keys=[client_id], primaryjoin="TaxReport.client_id == Client.id")
    # assignee_manager = relationship('Manager', foreign_keys=[assignee_manager_id], primaryjoin="TaxReport.assignee_manager_id == Manager.id")
    # file = relationship('File', foreign_keys=[file_id], primaryjoin="TaxReport.file_id == File.id")
    # tasks = relationship('ClientTask', cascade="all, delete-orphan")


class ClientTask(base.ModelBase):
    __tablename__ = 'client_tasks'
    __to_dict__ = ['manager']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))

    # Task details
    description = sa.Column(sa.Text)
    date = sa.Column(sa.DateTime(timezone=True))
    name = sa.Column(sa.String, nullable=False)
    due_date = sa.Column(sa.DateTime(timezone=True))
    status = sa.Column(sa.String, default='OPEN')  # OPEN, IN_PROGRESS, COMPLETED
    client_service_id = sa.Column(sa.Integer, sa.ForeignKey('client_services.id'), nullable=True)
    tax_report_id = sa.Column(sa.Integer, sa.ForeignKey('tax_reports.id'), nullable=True)
    manager_id = sa.Column(sa.String(36), sa.ForeignKey('managers.id'))

    # notification/burning status tracking
    is_burning = sa.Column(sa.Boolean, default=False)  # For showing "подгорающие" tasks
    notification_triggered_at = sa.Column(sa.DateTime(timezone=True))  # When notification was first triggered
    notification_dismissed_at = sa.Column(sa.DateTime(timezone=True))  # When notification was manually dismissed

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # client = relationship('Client', foreign_keys=[client_id], primaryjoin="ClientTask.client_id == Client.id")
    # assignee_manager = relationship('Manager', foreign_keys=[manager_id], primaryjoin="ClientTask.manager_id == Manager.id")


class Source(base.ModelBase):
    __tablename__ = 'sources'

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    title = sa.Column(sa.String)


class File(base.ModelBase):
    __tablename__ = 'files'

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    date = sa.Column(sa.DateTime(timezone=True))
    name = sa.Column(sa.String, index=True)
    description = sa.Column(sa.Text)
    hash = sa.Column(sa.String(64), index=True)
    file_type = sa.Column(sa.String(100))
    doc_type = sa.Column(sa.String)
    client_id = sa.Column(sa.String(36))
    client_person_id = sa.Column(sa.String(36))
    manager_id = sa.Column(sa.String(36))

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class NotificationRule(base.ModelBase):
    __tablename__ = 'notification_rules'

    id = sa.Column(sa.Integer, primary_key=True)
    title = sa.Column(sa.String, nullable=False)
    description = sa.Column(sa.Text)
    object_type = sa.Column(sa.String, nullable=False)  # Task, TaxReport
    object_id = sa.Column(sa.Integer, nullable=True)  # Specific object ID if needed
    offset_days = sa.Column(sa.Integer, nullable=False)
    frequency = sa.Column(sa.String, nullable=False)  # once, daily, weekly
    repeat_until_due = sa.Column(sa.Boolean, default=False)
    channels = sa.Column(PostgresJSON)  # ["email", "slack", "sms", "telegram", "in_app"]
    active = sa.Column(sa.Boolean, default=True)
    schedule_config = sa.Column(PostgresJSON)  # Cron-like schedule configuration

    # Conditions for triggering (can be extended)
    conditions = sa.Column(PostgresJSON)  # Additional conditions for triggering notifications

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # notifications = relationship('Notification', cascade="all, delete-orphan", back_populates='notification_rule')


class Notification(base.ModelBase):
    __tablename__ = 'notifications'

    id = sa.Column(sa.Integer, primary_key=True)
    rule_id = sa.Column(sa.Integer, sa.ForeignKey('notification_rules.id'), nullable=False)
    target_user_id = sa.Column(sa.BigInteger, nullable=False)
    object_type = sa.Column(sa.String, nullable=False)  # Task, TaxReport
    object_id = sa.Column(sa.Integer, nullable=False)  # ID of Task or TaxReport

    # Notification content
    title = sa.Column(sa.String, nullable=False)
    message = sa.Column(sa.Text)
    channel = sa.Column(sa.String, nullable=False)  # email, slack, sms, telegram, in_app

    # Status tracking
    status = sa.Column(sa.String, default='PENDING')  # PENDING, SENT, FAILED, DISMISSED
    sent_at = sa.Column(sa.DateTime(timezone=True))
    dismissed_at = sa.Column(sa.DateTime(timezone=True))
    dismissed_by_user_id = sa.Column(sa.BigInteger, nullable=True)

    # Scheduling for repeats
    next_notification_at = sa.Column(sa.DateTime(timezone=True))

    # Delivery details
    delivery_details = sa.Column(PostgresJSON)  # Channel-specific delivery info

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    # notification_rule = relationship(
    #     'NotificationRule', foreign_keys=[rule_id], primaryjoin="Notification.rule_id == NotificationRule.id", back_populates='notifications'
    # )


# Define Client model
class Client(base.ModelBase):
    __tablename__ = 'clients'

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    accounting_method = sa.Column(sa.String)
    active_since = sa.Column(sa.DateTime(timezone=True))
    agr_signed = sa.Column(sa.DateTime(timezone=True))
    agreement_sum = sa.Column(sa.Float)
    billing_method = sa.Column(sa.String)
    bookkeeping = sa.Column(sa.Boolean)
    business_model = sa.Column(sa.String(63))
    name = sa.Column(sa.String)
    company_phone = sa.Column(sa.String)
    cpa = sa.Column(sa.String)
    description = sa.Column(sa.Text)
    dissolution_date = sa.Column(sa.DateTime(timezone=True))
    ein = sa.Column(sa.String)
    fedtaxforms = sa.Column(sa.String)
    financial_year_end = sa.Column(sa.String)
    financial_year_end_for_subsidiary = sa.Column(sa.String)
    incorp_by = sa.Column(sa.String)
    internal_draft_flag = sa.Column(sa.Boolean, default=False)
    legal_ent_type = sa.Column(sa.String)
    login = sa.Column(sa.String)
    manager_id = sa.Column(sa.String(36))
    monthly_bill = sa.Column(sa.Float)
    naicscode = sa.Column(sa.String)
    notes_accounting = sa.Column(sa.Text)
    notes_address = sa.Column(sa.Text)
    notes_agreement = sa.Column(sa.Text)
    notes_contacts = sa.Column(sa.Text)
    notes_main = sa.Column(sa.Text)
    notes_shareholders = sa.Column(sa.Text)
    optional_share_count = sa.Column(sa.Integer)
    paid_by = sa.Column(sa.String)
    paid_by_mail = sa.Column(sa.String)
    password = sa.Column(sa.String)
    payroll = sa.Column(sa.Boolean)
    renewal_date = sa.Column(sa.DateTime(timezone=True))
    renewal_date_mail = sa.Column(sa.DateTime(timezone=True))
    since = sa.Column(sa.DateTime(timezone=True))
    source_id = sa.Column(sa.String(36))
    statetaxforms = sa.Column(sa.String)
    status = sa.Column(sa.String)
    subjurisd = sa.Column(sa.String)
    subsidiary_legal_entity_type = sa.Column(sa.String)
    subsidiary_to_consolidate = sa.Column(sa.String)
    total_shares = sa.Column(sa.Integer)
    withdrawal_date = sa.Column(sa.DateTime(timezone=True))

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # Relationships
    addresses = relationship('ClientAddress', cascade="all, delete-orphan")
    tax_reporting = relationship('TaxReporting', cascade="all, delete-orphan")
    tax_reports = relationship('TaxReport', cascade="all, delete-orphan")
    debit_cards = relationship('DebitCard', cascade="all, delete-orphan")
    payment_services = relationship('PaymentService', cascade="all, delete-orphan")
    contacts = relationship('ClientContact', cascade="all, delete-orphan")
    shareholders = relationship('ClientLLCShareholder', cascade="all, delete-orphan")
    tasks = relationship('ClientTask', cascade="all, delete-orphan")
    # client_files = relationship('ClientFile', back_populates='client_backref', cascade="all, delete-orphan")
    # services = relationship("Service", secondary=client_services, back_populates="clients")


class ClientTimeline(base.ModelBase):
    __tablename__ = 'client_timelines'

    id = sa.Column(sa.Integer, primary_key=True)
    external_id = sa.Column(sa.Integer)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    type = sa.Column(sa.String(100))
    note = sa.Column(sa.Text)

    __table_args__ = (
        sa.UniqueConstraint('client_id', 'type', name='client_timelines_unique'),
    )
    # client = relationship('Client', back_populates='timelines')


class ClientExtractor(base.ModelBase):
    __tablename__ = 'client_extractors'

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    external_id = sa.Column(sa.Integer)
    type = sa.Column(sa.String(100))
    note = sa.Column(sa.Text)

    __table_args__ = (
        sa.UniqueConstraint('client_id', 'type', name='client_extractor_unique'),
    )
    # client = relationship('Client', back_populates='extractors')


class ExternalID(base.ModelBase):
    __tablename__ = 'external_ids'

    id = sa.Column(sa.Integer, primary_key=True)

    internal_id = sa.Column(sa.String(36))
    internal_type = sa.Column(sa.String(100))

    external_app_type = sa.Column(sa.String(100))
    external_app_id = sa.Column(sa.String(36))
    external_item_id = sa.Column(sa.String(36))
    external_item_type = sa.Column(sa.String(100))

    additional_info = sa.Column(PostgresJSON, default=None, nullable=True)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class ClientAddress(base.ModelBase):
    __tablename__ = 'client_addresses'
    __to_dict__ = ['address']

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    address_type = sa.Column(sa.String)
    address_id = sa.Column(sa.String(36))
    renewal_date = sa.Column(sa.DateTime(timezone=True))
    phone = sa.Column(sa.String(20))
    paid_by = sa.Column(sa.String)
    note = sa.Column(sa.Text)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # client = relationship('Client', back_populates='addresses')
    address = relationship(
        'Address', foreign_keys=[address_id], primaryjoin="ClientAddress.address_id == Address.id"
    )


class CommonExtractor(base.ModelBase):
    __tablename__ = 'common_extractors'

    id = sa.Column(sa.Integer, primary_key=True)
    external_id = sa.Column(sa.Integer)
    title = sa.Column(sa.String)
    description = sa.Column(sa.Text)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class TaxReporting(base.ModelBase):
    __tablename__ = 'tax_reporting'

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    year = sa.Column(sa.String)
    reporting_1099 = sa.Column(sa.String)
    tax_return_by = sa.Column(sa.String)
    note = sa.Column(sa.Text)
    files = sa.Column(sa.String)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # client = relationship('Client', back_populates='tax_reporting')


class ClientContact(base.ModelBase):
    __tablename__ = 'client_contacts'
    __to_dict__ = ['person']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    client_person_id = sa.Column(sa.String)
    position = sa.Column(sa.String)
    is_main = sa.Column(sa.Boolean, default=False)
    email = sa.Column(sa.String)
    phone = sa.Column(sa.String)
    pcm = sa.Column(sa.String)
    note = sa.Column(sa.Text)

    person = relationship('Person', foreign_keys=[client_person_id], primaryjoin="ClientContact.client_person_id == Person.id")
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    # client = relationship('Client', back_populates='contacts')


class ClientLLCShareholder(base.ModelBase):
    __tablename__ = 'client_llc_shareholders'
    __to_dict__ = ['person']

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    client_person_id = sa.Column(sa.String)
    # Fields for the shareholders
    position = sa.Column(sa.String)
    ownership = sa.Column(sa.String)
    is_managing_member = sa.Column(sa.Boolean, default=False)
    note = sa.Column(sa.Text)

    person = relationship('Person', foreign_keys=[client_person_id], primaryjoin="ClientLLCShareholder.client_person_id == Person.id")


class DebitCard(base.ModelBase):
    __tablename__ = 'debit_cards'
    __encrypt_columns__ = ['debit_card', 'cid', 'card_holder', 'exp']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    # Fields for debit cards
    debit_card = sa.Column(sa.String)
    last_4_digits = sa.Column(sa.String)
    expired_at = sa.Column(sa.DateTime(timezone=True))
    cid = sa.Column(sa.String)
    linked_to = sa.Column(sa.String)
    card_holder = sa.Column(sa.String)
    exp = sa.Column(sa.String)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # client = relationship('Client', back_populates='debit_cards')


class PaymentService(base.ModelBase):
    __tablename__ = 'payment_services'
    __encrypt_columns__ = ['login_pass']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    # Fields for payment systems
    payment_system = sa.Column(sa.String)
    date_opened = sa.Column(sa.DateTime(timezone=True))
    opened_by = sa.Column(sa.String)
    email_connected = sa.Column(sa.String)
    responsible_person = sa.Column(sa.String)
    login_pass = sa.Column(sa.String)
    note = sa.Column(sa.Text)

    # client = relationship('Client', back_populates='payment_systems')


class BankAccount(base.ModelBase):
    __tablename__ = 'bank_accounts'
    __to_dict__ = ['authorized_signers', 'bank_cards']

    id = sa.Column(sa.String(36), primary_key=True, default=utils.generate_unicode_uuid)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    bank_name = sa.Column(sa.String)
    aba_number = sa.Column(sa.String(16))
    account_number = sa.Column(sa.String(32))
    bank_contact = sa.Column(sa.String)
    controlled_by = sa.Column(sa.String)
    date_opened = sa.Column(sa.DateTime(timezone=True))
    last_renewal = sa.Column(sa.DateTime(timezone=True))
    notes = sa.Column(sa.Text)

    # client = relationship('Client', back_populates='bank_accounts')
    # Do not edit
    authorized_signer_personss = relationship('BankAccountAuthorizedSigner', cascade="all, delete-orphan")
    bank_cardss = relationship('BankCard', cascade="all, delete-orphan")


class BankAccountAuthorizedSigner(base.ModelBase):
    __tablename__ = 'bank_account_authorized_signers'
    __to_dict__ = ['person']

    id = sa.Column(sa.Integer, primary_key=True)
    bank_account_id = sa.Column(sa.String(36), sa.ForeignKey('bank_accounts.id'))
    client_person_id = sa.Column(sa.String(36), sa.ForeignKey('client_persons.id'))

    person = relationship('Person', foreign_keys=[client_person_id], primaryjoin="BankAccountAuthorizedSigner.client_person_id == Person.id")


class BankCard(base.ModelBase):
    __tablename__ = 'bank_cards'
    __encrypt_columns__ = ['card_number', 'cvv', 'card_holder', 'valid_through']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    bank_account_id = sa.Column(sa.String(36), sa.ForeignKey('bank_accounts.id'))
    card_number = sa.Column(sa.String)
    last_4_digits = sa.Column(sa.String)
    cvv = sa.Column(sa.String)
    card_holder_name = sa.Column(sa.String)
    valid_through = sa.Column(sa.String)
    expired_at = sa.Column(sa.DateTime(timezone=True))

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class ClientAuthorizedSigner(base.ModelBase):
    __tablename__ = 'client_authorized_signers'
    __to_dict__ = ['person']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    client_person_id = sa.Column(sa.String(36), sa.ForeignKey('client_persons.id'))
    note = sa.Column(sa.Text)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # client = relationship('Client', back_populates='authorized_signers')
    person = relationship('Person', foreign_keys=[client_person_id], primaryjoin="ClientAuthorizedSigner.client_person_id == Person.id")


class ClientRegistration(base.ModelBase):
    __tablename__ = 'client_registrations'
    __to_dict__ = ['registered_agent']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    registered_agent_id = sa.Column(sa.String(36), sa.ForeignKey('reg_agents.id'))
    is_primary = sa.Column(sa.Boolean)
    registered_date = sa.Column(sa.DateTime(timezone=True))
    terminated_date = sa.Column(sa.DateTime(timezone=True))
    last_renewal_date = sa.Column(sa.DateTime(timezone=True))
    annual_compliance_due_date = sa.Column(sa.DateTime(timezone=True))
    state_of_incorporation = sa.Column(sa.String)
    billed_to = sa.Column(sa.String)
    last_soi_filed = sa.Column(sa.DateTime(timezone=True))
    state_entity = sa.Column(sa.String)
    notes = sa.Column(sa.Text)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)

    # client = relationship('Client', back_populates='registrations')
    registered_agent = relationship('RegAgent')


class ClientShare(base.ModelBase):
    __tablename__ = 'client_shares'

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    type = sa.Column(sa.String(20))
    stock_authorized = sa.Column(sa.Integer)
    stock_issued = sa.Column(sa.Integer)
    notes = sa.Column(sa.Text)

    # client = relationship('Client', back_populates='share_classes')


class ClientCapitalization(base.ModelBase):
    __tablename__ = 'client_capitalizations'
    __to_dict__ = ['person', 'share']

    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'))
    person_id = sa.Column(sa.String(36), sa.ForeignKey('client_persons.id'))
    share_id = sa.Column(sa.Integer, sa.ForeignKey('client_shares.id'), nullable=True)
    share_amount = sa.Column(sa.Integer)
    date = sa.Column(sa.DateTime(timezone=True))
    vesting = sa.Column(sa.String)
    issued_percentage = sa.Column(sa.Float)
    authorized_percentage = sa.Column(sa.Float)
    notes = sa.Column(sa.Text)

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class ChangeRecord(base.ModelBase):
    """
    This model is used to store changes to the client data.
    It is used to track changes to the client data.
    """
    __tablename__ = 'change_records'
    id = sa.Column(sa.Integer, primary_key=True)
    client_id = sa.Column(sa.String(36), sa.ForeignKey('clients.id'), index=True)
    manager_id = sa.Column(sa.String(36), sa.ForeignKey('managers.id'))

    old_state = sa.Column(PostgresJSONB)
    new_state = sa.Column(PostgresJSONB)
    type = sa.Column(sa.String(20))
    object_id = sa.Column(sa.String(36))

    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now, index=True)


class CP575(base.ModelBase):
    __tablename__ = 'cp575'
    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    content = sa.Column(PostgresJSON)
    client_id = sa.Column(sa.String(36), index=True)


class PersonalDocument(base.ModelBase):
    __tablename__ = 'personal_documents'
    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    client_person_id = sa.Column(sa.String(36), index=True)
    document_type = sa.Column(sa.String(200), nullable=False)
    content = sa.Column(PostgresJSON)  # Document-specific details (e.g., passport number, address verification)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class LegalCorporateDocument(base.ModelBase):
    __tablename__ = 'legal_corporate_documents'
    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    client_id = sa.Column(sa.String(36), nullable=False, index=True)
    company_name = sa.Column(sa.String(255), nullable=False)
    legal_entity_type = sa.Column(sa.String(50), nullable=False)
    state_of_registration = sa.Column(sa.CHAR(2), nullable=False)
    file_number = sa.Column(sa.String(50), nullable=False)
    filed_date = sa.Column(sa.Date, nullable=False)
    registered_office_address = sa.Column(sa.String(255), nullable=False)
    registered_agent_name = sa.Column(sa.String(255), nullable=False)
    authorized_shares = sa.Column(sa.Integer, default=0)
    par_value_per_share = sa.Column(sa.String(20), nullable=False)
    created_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)
    updated_at = sa.Column(sa.DateTime(timezone=True), nullable=True, default=now)


class FormSS4(base.ModelBase):
    __tablename__ = 'form_ss4'
    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    client_id = sa.Column(sa.String(36), nullable=False, index=True)
    mailing_address = sa.Column(sa.String(255), nullable=False)
    legal_type = sa.Column(sa.String(50), nullable=False)
    closing_month = sa.Column(sa.String(10), nullable=False)  # Financial Year End (Closing month of accounting year)
    principal_activity = sa.Column(sa.String(255), nullable=False)
    products_or_services = sa.Column(sa.String(255), nullable=False)
    tax_id_ein = sa.Column(sa.String(50), unique=True, nullable=False)
