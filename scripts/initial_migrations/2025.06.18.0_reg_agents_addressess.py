"""reg agents addressess

Revision ID: 2025.06.18.0
Revises: 2025.06.17.2
Create Date: 2025-06-18 10:50:01.227262

"""
from typing import Sequence, Union, Optional

import pydantic
from alembic import op
import sqlalchemy as sa
from langchain_core.prompts import PromptTemplate
from langchain_openai import Chat<PERSON>penAI

# revision identifiers, used by Alembic.
revision: str = '2025.06.18.0'
down_revision: Union[str, None] = '2025.06.17.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


class NormalizedAddress(pydantic.BaseModel):
    street: Optional[str] = None
    pobox: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip: Optional[str] = None
    country: Optional[str] = None
    full_address: Optional[str] = None


def normalize_address(address: str) -> NormalizedAddress:
    llm = ChatOpenAI(model_name='gpt-4o-mini').with_structured_output(NormalizedAddress, method="json_schema")

    prompt_tpl = (
        "Normalize the given address, extract street, pobox, city, state, zip, and country.\n"
        "If something is unknown, try to infer it from the context.\n"
        "Compile the full_address in form [street], [city], [state] [zip], [country].\n"
        "Always explicitly include full_address.\n"

        "The address is: {address}"
    )
    chain = PromptTemplate.from_template(prompt_tpl) | llm

    normalized_address = chain.invoke({'address': address})
    country = normalized_address.country
    if not country:
        normalized_address.country = 'USA' if normalized_address.zip and normalized_address.zip.isdigit() else None
    if not normalized_address.full_address:
        normalized_address.full_address = (
            f"{normalized_address.street}, {normalized_address.city}, "
            f"{normalized_address.state} {normalized_address.zip}, {normalized_address.country}"
        )
    return normalized_address


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reg_agents', sa.Column('address_id', sa.String(length=36), nullable=True))
    op.create_foreign_key(None, 'reg_agents', 'addresses', ['address_id'], ['id'])
    conn = op.get_bind()
    all_reg_agents = conn.execute(sa.text("SELECT id, address FROM reg_agents")).fetchall()
    found = 0
    not_found = []
    missing_addr_map = {}
    for reg_agent in all_reg_agents:
        id, address = reg_agent
        if not address:
            found += 1
            continue
        address = address.strip()
        # Find address
        address_db = conn.execute(sa.text("SELECT id FROM addresses WHERE full_address = :address"), parameters={'address': address}).fetchone()
        if address_db:
            # conn.execute(sa.text("UPDATE reg_agents SET address_id = :address_id WHERE id = :id"), parameters={'address_id': address_db[0], 'id': id})
            found += 1
        else:
            print(f'Not found address: {address}')
            not_found.append(address)
            missing_addr_map[id] = address

    print(f"Found {found} / {len(all_reg_agents)} addresses")
    print(f"Missing unique addresses: {len(set(not_found))}")
    raise ValueError("Stop")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'reg_agents', type_='foreignkey')
    op.drop_column('reg_agents', 'address_id')
    # ### end Alembic commands ###
